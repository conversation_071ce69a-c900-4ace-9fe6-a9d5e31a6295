# Cube1 Group 系统图表文档

## 概述

本目录包含 Cube1 Group 网格数据可视化系统的架构图表和时序图，帮助开发者理解系统的整体设计和交互流程。

## 图表列表

### 1. [系统架构图](./architecture.md)

- **文件**: `architecture.md`
- **类型**: 系统架构图 (Mermaid Graph)
- **描述**: 展示系统的整体架构设计，包括各个层次和组件的关系
- **内容**:
  - 用户界面层 (UI Layer)
  - 状态管理层 (State Management)
  - 业务逻辑层 (Business Logic)
  - 数据层 (Data Layer)
  - 后端服务层 (Backend Services)
  - 基础设施层 (Infrastructure)

### 2. [系统时序图](./sequence.md)

- **文件**: `sequence.md`
- **类型**: 时序图 (Mermaid Sequence)
- **描述**: 展示系统主要交互流程和数据流向
- **内容**:
  - 应用初始化流程
  - 模式切换流程
  - 单元格交互流程
  - 数据更新流程
  - API健康检查流程
  - 性能优化流程

### 3. [模块内部详细架构图](./module-details.md)

- **文件**: `module-details.md`
- **类型**: 详细架构图 (Mermaid Graph)
- **描述**: 展示各核心模块的内部结构和实现细节
- **内容**:
  - MatrixStore (状态管理模块)
  - MatrixCore (核心引擎模块)
  - MatrixTypes (类型系统模块)
  - GroupAData (数据层模块)
  - Matrix Component (UI组件模块)
  - Controls Component (控制组件模块)
  - Button Component (按钮组件模块)
  - FastAPI Backend (后端服务模块)

## 系统概览

### 核心特性

- **高性能渲染**: 33x33网格（1089个单元格）实时渲染
- **多业务模式**: 支持坐标、颜色、等级、词语四种显示模式
- **响应式状态管理**: 基于Zustand的高效状态管理
- **模块化架构**: 清晰的分层设计，易于维护和扩展

### 技术栈

- **前端**: Next.js 15.1.0 + React 18.3.1 + TypeScript 5.8.3
- **状态管理**: Zustand 5.0.6 + 持久化中间件
- **后端**: FastAPI 0.116.1 + Python 3.11+
- **构建工具**: Turbo 2.3.0 + pnpm 9.15.0
- **样式**: Tailwind CSS 3.4.17

### 架构亮点

#### 1. 数据驱动设计

- 统一的数据模型和类型系统
- 配置驱动的业务模式切换
- 高效的数据缓存机制

#### 2. 性能优化

- 计算属性缓存
- 批量状态更新
- 智能重新渲染

#### 3. 可扩展性

- 插件式业务模式处理器
- 模块化的组件设计
- 清晰的接口定义

#### 4. 开发体验

- 完整的TypeScript类型支持
- 热重载开发环境
- 自动化测试和构建

## 使用说明

### 查看图表

1. 点击上方的图表链接查看详细内容
2. 图表使用Mermaid语法，支持在GitHub、VS Code等环境中直接渲染
3. 可以复制Mermaid代码到在线编辑器进行编辑

### 更新图表

1. 修改对应的`.md`文件中的Mermaid代码
2. 确保语法正确性
3. 更新文档的生成时间和版本信息

### 图表工具

- **在线编辑器**: [Mermaid Live Editor](https://mermaid.live/)
- **VS Code插件**: Mermaid Preview
- **文档生成**: 支持导出为PNG、SVG等格式

## 相关文档

- [项目README](../../README.md) - 项目总体介绍
- [开发指南](../../CLAUDE.md) - 开发指导文档
- [API文档](../report/) - API接口文档
- [测试文档](../../apps/frontend/tests/) - 测试相关文档

## 维护说明

### 更新频率

- 架构图：重大架构变更时更新
- 时序图：新增主要交互流程时更新
- 模块详细图：模块内部结构变更时更新
- 文档：每次图表更新时同步更新

### 版本管理

- 使用语义化版本号
- 记录更新时间和变更内容
- 保持与代码实现的同步

---

*最后更新: 2025-01-25*
*维护者: Augment Agent*
*版本: v1.0*
